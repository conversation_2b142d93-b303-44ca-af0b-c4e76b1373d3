"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import { useParams } from "next/navigation";
import JC_List from "../../../components/JC_List/JC_List";
import { PropertyModel } from "../../../models/Property";
import { PropertyDefectModel } from "../../../models/PropertyDefect";
import { DefectImageModel } from "../../../models/DefectImage";
import { O_AreaModel } from "../../../models/O_Area";
import { O_LocationModel } from "../../../models/O_Location";
import { O_OrientationModel } from "../../../models/O_Orientation";
import { O_SeverityModel } from "../../../models/O_Severity";
import { JC_Utils, JC_Utils_Rooms, JC_Utils_Defects } from "../../../Utils";
import { JC_ListHeader } from "../../../components/JC_List/JC_ListHeader";
import JC_Spinner from "../../../components/JC_Spinner/JC_Spinner";

export default function DefectsEditPage() {
    const params = useParams();
    const propertyId = params.id as string;

    // - STATE - //
    const [property, setProperty] = useState<PropertyModel | null>(null);
    const [selectedRoom, setSelectedRoom] = useState<string>("");
    const [propertyDefects, setPropertyDefects] = useState<PropertyDefectModel[]>([]);
    const [defectImages, setDefectImages] = useState<DefectImageModel[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [initialised, setInitialised] = useState<boolean>(false);

    // Option lists
    const [areaOptions, setAreaOptions] = useState<O_AreaModel[]>([]);
    const [locationOptions, setLocationOptions] = useState<O_LocationModel[]>([]);
    const [orientationOptions, setOrientationOptions] = useState<O_OrientationModel[]>([]);
    const [severityOptions, setSeverityOptions] = useState<O_SeverityModel[]>([]);

    // - LOAD DATA - //
    async function loadData() {
        try {
            setIsLoading(true);

            // Load property and option lists
            const [
                propertyData,
                areasData,
                locationsData,
                orientationsData,
                severitiesData
            ] = await Promise.all([
                PropertyModel.Get(propertyId),
                O_AreaModel.GetList(),
                O_LocationModel.GetList(),
                O_OrientationModel.GetList(),
                O_SeverityModel.GetList()
            ]);

            setProperty(propertyData);
            setAreaOptions(areasData);
            setLocationOptions(locationsData);
            setOrientationOptions(orientationsData);
            setSeverityOptions(severitiesData);

        } catch (error) {
            console.error('Error loading data:', error);
        } finally {
            setIsLoading(false);
            setInitialised(true);
        }
    }

    async function loadPropertyDefects(propertyId: string) {
        try {
            setIsLoading(true);

            // Load defects and images for this property
            const [defectsData, imagesData] = await Promise.all([
                PropertyDefectModel.GetList({ PropertyId: propertyId }),
                DefectImageModel.GetList({ PropertyId: propertyId })
            ]);

            setPropertyDefects(defectsData);
            setDefectImages(imagesData);

        } catch (error) {
            console.error('Error loading property defects:', error);
        } finally {
            setIsLoading(false);
        }
    }

    // Load data on mount
    useEffect(() => {
        if (propertyId) {
            loadData();
            loadPropertyDefects(propertyId);
        }
    }, [propertyId]);

    // - HANDLERS - //
    function handleRoomSelection(roomName: string) {
        setSelectedRoom(roomName);
    }

    // - COMPUTED VALUES - //
    const selectedRooms = property ?
        JC_Utils_Rooms.parseRoomsJson(property.RoomsListJson) : [];

    const selectedRoomNames = property ?
        JC_Utils_Rooms.getSelectedRoomNames(selectedRooms, []) : [];

    // Filter defects by selected room
    const filteredDefects = selectedRoom
        ? propertyDefects.filter(defect => {
            const defectRooms = JC_Utils_Defects.parseDefectRoomsJson(defect.RoomsJson);
            return defectRooms.includes(selectedRoom);
        })
        : propertyDefects;

    // Add image count to defects
    const defectsWithImageCount = filteredDefects.map(defect => ({
        ...defect,
        Images: defectImages.filter(img => img.PropertyDefectId === defect.Id).length
    }));

    // Helper functions to get option names
    const getAreaName = (code: string) => areaOptions.find(o => o.Code === code)?.Name || code;
    const getLocationName = (code: string) => locationOptions.find(o => o.Code === code)?.Name || code;
    const getOrientationName = (code: string) => orientationOptions.find(o => o.Code === code)?.Name || code;
    const getSeverityName = (code: string) => severityOptions.find(o => o.Code === code)?.Name || code;

    // List headers for defects table
    const defectHeaders: JC_ListHeader[] = [
        { sortKey: "AreaCode", label: "Area" },
        { sortKey: "LocationCode", label: "Location" },
        { sortKey: "OrientationCode", label: "Orientation" },
        { sortKey: "Defects", label: "Defects" },
        { sortKey: "ServerityCode", label: "Severity" },
        { sortKey: "Images", label: "Images" }
    ];

    // - RENDER - //
    if (!initialised) {
        return <JC_Spinner isPageBody />;
    }

    return (
        <div className={styles.mainContainer}>
            {/* Header */}
            <div className={styles.header}>
                <h2 className={styles.headerLabel}>
                    Defects - {property?.Address || 'Loading...'}
                </h2>
            </div>

            <div className={styles.contentContainer}>
                {/* Left Panel - Rooms */}
                <div className={styles.leftPanel}>
                    <div className={styles.roomsHeader}>Rooms</div>
                    <div className={styles.roomsList}>
                        <div
                            className={`${styles.roomItem} ${selectedRoom === "" ? styles.selectedRoom : ""}`}
                            onClick={() => handleRoomSelection("")}
                        >
                            All Rooms
                        </div>
                        {selectedRoomNames.map((roomName, index) => (
                            <div
                                key={index}
                                className={`${styles.roomItem} ${selectedRoom === roomName ? styles.selectedRoom : ""}`}
                                onClick={() => handleRoomSelection(roomName)}
                            >
                                {roomName}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Panel - Defects */}
                <div className={styles.rightPanel}>
                    {!property ? (
                        <div className={styles.noRoomSelected}>
                            Loading property...
                        </div>
                    ) : defectsWithImageCount.length === 0 ? (
                        <div className={styles.noDefects}>
                            {isLoading ? "Loading defects..." : "No defects found"}
                        </div>
                    ) : (
                        <div className={styles.defectsContainer}>
                            <div className={styles.defectsTable}>
                                <JC_List
                                    overrideClass="defectsListOverride"
                                    items={defectsWithImageCount}
                                    headers={defectHeaders}
                                    defaultSortKey="AreaCode"
                                    defaultSortDirection="asc"
                                    row={(defect: PropertyDefectModel & { Images: number }) => (
                                        <>
                                            <td>{getAreaName(defect.AreaCode)}</td>
                                            <td>{getLocationName(defect.LocationCode)}</td>
                                            <td>{getOrientationName(defect.OrientationCode)}</td>
                                            <td className="defectsColumn">{defect.Defects}</td>
                                            <td>{getSeverityName(defect.ServerityCode)}</td>
                                            <td className="imagesColumn">{defect.Images}</td>
                                        </>
                                    )}
                                />
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
