"use client"

import { useEffect, useState, useCallback } from "react";
import { useRouter, useParams } from "next/navigation";
import JC_FormTablet, { JC_FormTabletModel } from "../../../components/JC_FormTablet/JC_FormTablet";
import JC_Spinner from "../../../components/JC_Spinner/JC_Spinner";
import { PropertyModel } from "../../../models/Property";
import { O_BuildingTypeModel } from "../../../models/O_BuildingType";
import { O_OrientationModel } from "../../../models/O_Orientation";
import { O_NumBedroomsModel } from "../../../models/O_NumBedrooms";
import { O_StoreysModel } from "../../../models/O_Storeys";
import { O_FurnishedModel } from "../../../models/O_Furnished";
import { O_OccupiedModel } from "../../../models/O_Occupied";
import { O_CompanyStrataTitleModel } from "../../../models/O_CompanyStrataTitle";
import { O_FloorModel } from "../../../models/O_Floor";
import { O_OtherBuildingElementsModel } from "../../../models/O_OtherBuildingElements";
import { O_OtherTimberBldgElementsModel } from "../../../models/O_OtherTimberBldgElements";
import { O_RoofModel } from "../../../models/O_Roof";
import { O_WallsModel } from "../../../models/O_Walls";
import { O_WeatherModel } from "../../../models/O_Weather";
import { FieldTypeEnum } from "../../../enums/FieldType";
import { JC_FieldModel } from "../../../models/ComponentModels/JC_Field";
import { JC_FieldOption } from "../../../models/ComponentModels/JC_FieldOption";
import { JC_Utils } from "@/app/Utils";
import { LocalStorageKeyEnum } from "../../../enums/LocalStorageKey";



export default function PropertyEditPage() {
    const router = useRouter();
    const params = useParams();
    const propertyId = params.id as string;
    const isNewRecord = propertyId === "new";

    // - STATE - //
    const [initialised, setInitialised] = useState<boolean>(false);
    const [currentProperty, setCurrentProperty] = useState<PropertyModel>(new PropertyModel());
    const [isLoading, setIsLoading] = useState<boolean>(false);

    // Option lists
    const [buildingTypeOptions, setBuildingTypeOptions] = useState<JC_FieldOption[]>([]);
    const [companyStrataTitleOptions, setCompanyStrataTitleOptions] = useState<JC_FieldOption[]>([]);
    const [orientationOptions, setOrientationOptions] = useState<JC_FieldOption[]>([]);
    const [numBedroomsOptions, setNumBedroomsOptions] = useState<JC_FieldOption[]>([]);
    const [storeysOptions, setStoreysOptions] = useState<JC_FieldOption[]>([]);
    const [furnishedOptions, setFurnishedOptions] = useState<JC_FieldOption[]>([]);
    const [occupiedOptions, setOccupiedOptions] = useState<JC_FieldOption[]>([]);
    const [floorOptions, setFloorOptions] = useState<JC_FieldOption[]>([]);
    const [otherBuildingElementsOptions, setOtherBuildingElementsOptions] = useState<JC_FieldOption[]>([]);
    const [otherTimberBldgElementsOptions, setOtherTimberBldgElementsOptions] = useState<JC_FieldOption[]>([]);
    const [roofOptions, setRoofOptions] = useState<JC_FieldOption[]>([]);
    const [wallsOptions, setWallsOptions] = useState<JC_FieldOption[]>([]);
    const [weatherOptions, setWeatherOptions] = useState<JC_FieldOption[]>([]);

    // Convert option models to JC_FieldOption format
    const convertToFieldOptions = (models: any[]): JC_FieldOption[] => {
        return models.map(model => ({
            OptionId: model.Code,
            Label: model.Name,
            Selected: false
        }));
    };

    // Load options data
    const loadOptions = useCallback(async () => {
        try {
            setIsLoading(true);

            const [
                buildingTypes,
                companyStrataTitles,
                orientations,
                numBedrooms,
                storeys,
                furnished,
                occupied,
                floors,
                otherBuildingElements,
                otherTimberBldgElements,
                roofs,
                walls,
                weather
            ] = await Promise.all([
                O_BuildingTypeModel.GetList(),
                O_CompanyStrataTitleModel.GetList(),
                O_OrientationModel.GetList(),
                O_NumBedroomsModel.GetList(),
                O_StoreysModel.GetList(),
                O_FurnishedModel.GetList(),
                O_OccupiedModel.GetList(),
                O_FloorModel.GetList(),
                O_OtherBuildingElementsModel.GetList(),
                O_OtherTimberBldgElementsModel.GetList(),
                O_RoofModel.GetList(),
                O_WallsModel.GetList(),
                O_WeatherModel.GetList()
            ]);

            setBuildingTypeOptions(convertToFieldOptions(buildingTypes || []));
            setCompanyStrataTitleOptions(convertToFieldOptions(companyStrataTitles || []));
            setOrientationOptions(convertToFieldOptions(orientations || []));
            setNumBedroomsOptions(convertToFieldOptions(numBedrooms || []));
            setStoreysOptions(convertToFieldOptions(storeys || []));
            setFurnishedOptions(convertToFieldOptions(furnished || []));
            setOccupiedOptions(convertToFieldOptions(occupied || []));
            setFloorOptions(convertToFieldOptions(floors || []));
            setOtherBuildingElementsOptions(convertToFieldOptions(otherBuildingElements || []));
            setOtherTimberBldgElementsOptions(convertToFieldOptions(otherTimberBldgElements || []));
            setRoofOptions(convertToFieldOptions(roofs || []));
            setWallsOptions(convertToFieldOptions(walls || []));
            setWeatherOptions(convertToFieldOptions(weather || []));

        } catch (error) {
            console.error('Error loading options:', error);
        } finally {
            setIsLoading(false);
            setInitialised(true);
        }
    }, []);



    // Load property data
    const loadProperty = useCallback(async () => {
        if (!isNewRecord) {
            try {
                setIsLoading(true);
                const property = await PropertyModel.Get(propertyId);
                if (property) {
                    setCurrentProperty(property);
                }
            } catch (error) {
                console.error('Error loading property:', error);
            } finally {
                setIsLoading(false);
            }
        }
    }, [propertyId, isNewRecord]);

    // Load data on mount
    useEffect(() => {
        loadOptions();
        loadProperty();
    }, [loadOptions, loadProperty]);

    // Handle form submission
    const handleSubmit = async () => {
        try {
            setIsLoading(true);

            let response;
            if (isNewRecord) {
                response = await PropertyModel.Create(currentProperty);
            } else {
                response = await PropertyModel.Update(currentProperty);
            }

            if (response) {
                router.push('/property');
            }
        } catch (error) {
            console.error('Error saving property:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Handle "Go to Defects" button click
    const handleGoToDefects = () => {
        if (!isNewRecord && currentProperty.Id) {
            localStorage.setItem(LocalStorageKeyEnum.JC_SelectedPropertyForDefects, currentProperty.Id);
            router.push('/defects');
        }
    };

    // Create the form tablet model
    const formTabletModel: JC_FormTabletModel = {
        headerLabel: `${isNewRecord ? "Create" : "Edit"} Property${currentProperty.Address ? ` - ${currentProperty.Address}` : ""}`,
        leftPaneHeader: "Property Fields",
        sections: [
            {
                Heading: "Property Details",
                Fields: [
                    {
                        inputId: "address",
                        type: FieldTypeEnum.Text,
                        label: "Address",
                        value: currentProperty.Address || "",
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, Address: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Address is required." : ""
                    },
                    {
                        inputId: "building-type",
                        type: FieldTypeEnum.Dropdown,
                        label: "Building Type",
                        value: currentProperty.BuildingTypeCode || "",
                        options: buildingTypeOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, BuildingTypeCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Building Type is required." : ""
                    },
                    {
                        inputId: "company-strata",
                        type: FieldTypeEnum.Dropdown,
                        label: "Company or Strata Title",
                        value: currentProperty.CompanyStrataTitleCode || "",
                        options: companyStrataTitleOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, CompanyStrataTitleCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Company or Strata Title is required." : ""
                    },
                    {
                        inputId: "bedrooms",
                        type: FieldTypeEnum.Dropdown,
                        label: "No. of Bedrooms",
                        value: currentProperty.NumBedroomsCode || "",
                        options: numBedroomsOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, NumBedroomsCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "No. of Bedrooms is required." : ""
                    },
                    {
                        inputId: "orientation",
                        type: FieldTypeEnum.Dropdown,
                        label: "Orientation",
                        value: currentProperty.OrientationCode || "",
                        options: orientationOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, OrientationCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Orientation is required." : ""
                    },
                    {
                        inputId: "storeys",
                        type: FieldTypeEnum.Dropdown,
                        label: "Storeys",
                        value: currentProperty.StoreysCode || "",
                        options: storeysOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, StoreysCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Storeys is required." : ""
                    }
                ]
            },
            {
                Heading: "Occupancy",
                Fields: [
                    {
                        inputId: "furnished",
                        type: FieldTypeEnum.Dropdown,
                        label: "Furnished",
                        value: currentProperty.FurnishedCode || "",
                        options: furnishedOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, FurnishedCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Furnished is required." : ""
                    },
                    {
                        inputId: "occupied",
                        type: FieldTypeEnum.Dropdown,
                        label: "Occupied",
                        value: currentProperty.OccupiedCode || "",
                        options: occupiedOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, OccupiedCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Occupied is required." : ""
                    }
                ]
            },
            {
                Heading: "Construction Method",
                Fields: [
                    {
                        inputId: "floor",
                        type: FieldTypeEnum.Dropdown,
                        label: "Floor",
                        value: currentProperty.FloorCode || "",
                        options: floorOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, FloorCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Floor is required." : ""
                    },
                    {
                        inputId: "other-building",
                        type: FieldTypeEnum.Dropdown,
                        label: "Other Building Elements",
                        value: currentProperty.OtherBuildingElementsCode || "",
                        options: otherBuildingElementsOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, OtherBuildingElementsCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Other Building Elements is required." : ""
                    },
                    {
                        inputId: "other-timber",
                        type: FieldTypeEnum.Dropdown,
                        label: "Other Timber Building Elements",
                        value: currentProperty.OtherTimberBldgElementsCode || "",
                        options: otherTimberBldgElementsOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, OtherTimberBldgElementsCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Other Timber Building Elements is required." : ""
                    },
                    {
                        inputId: "roof",
                        type: FieldTypeEnum.Dropdown,
                        label: "Roof",
                        value: currentProperty.RoofCode || "",
                        options: roofOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, RoofCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Roof is required." : ""
                    },
                    {
                        inputId: "walls",
                        type: FieldTypeEnum.Dropdown,
                        label: "Walls",
                        value: currentProperty.WallsCode || "",
                        options: wallsOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, WallsCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Walls is required." : ""
                    }
                ]
            },
            {
                Heading: "Inspection Conditions",
                Fields: [
                    {
                        inputId: "weather",
                        type: FieldTypeEnum.Dropdown,
                        label: "Weather",
                        value: currentProperty.WeatherCode || "",
                        options: weatherOptions,
                        onChange: (newValue) => setCurrentProperty(prev => ({ ...prev, WeatherCode: newValue })),
                        validate: (value) => JC_Utils.stringNullOrEmpty(value?.toString()) ? "Weather is required." : ""
                    }
                ]
            }
        ],
        submitButtonText: isNewRecord ? "Create" : "Save",
        onSubmit: handleSubmit,
        isLoading: isLoading,
        additionalFooterButtons: !isNewRecord && currentProperty.Id ? [
            {
                text: "Go to Defects",
                onClick: handleGoToDefects
            }
        ] : undefined
    };

    // - RENDER - //
    return !initialised
        ? (<JC_Spinner isPageBody />)
        : (
            <JC_FormTablet model={formTabletModel} />
        );
}
