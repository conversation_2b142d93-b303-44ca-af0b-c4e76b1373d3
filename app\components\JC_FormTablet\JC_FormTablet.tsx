"use client"

import styles from "./JC_FormTablet.module.scss";
import { useState } from "react";
import J<PERSON><PERSON><PERSON> from "../JC_Field/JC_Field";
import JC_Button from "../JC_Button/JC_Button";
import { JC_FieldModel } from "../../models/ComponentModels/JC_Field";
import { FieldTypeEnum } from "../../enums/FieldType";
import { JC_Utils } from "@/app/Utils";

export interface JC_FormTabletModel {
    headerLabel: string;
    leftPaneHeader: string;
    sections: JC_FormTabletSection[];
    submitButtonText?: string;
    onSubmit?: () => void;
    isLoading?: boolean;
    showSaveButton?: boolean;
    additionalFooterButtons?: Array<{
        text: string;
        onClick: () => void;
        isLoading?: boolean;
    }>;
}

export interface JC_FormTabletSection {
    Heading: string;
    Fields: JC_FieldModel[];
}

interface JC_FormTabletProps {
    model: JC_FormTabletModel;
}

export default function JC_FormTablet({ model }: JC_FormTabletProps) {
    const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
    const [submitClicked, setSubmitClicked] = useState<boolean>(false);

    // Get all fields from all sections
    const getAllFields = (): JC_FieldModel[] => {
        const allFields: JC_FieldModel[] = [];
        model.sections.forEach(section => {
            section.Fields.forEach(field => {
                allFields.push(field);
            });
        });
        return allFields;
    };

    // Get first validation error message
    const getFirstValidationError = (): string => {
        const allFields = getAllFields();
        for (const field of allFields) {
            if (field.validate) {
                const errorMessage = field.validate(field.value);
                if (!JC_Utils.stringNullOrEmpty(errorMessage)) {
                    return errorMessage;
                }
            }
        }
        return "";
    };

    // Check if a field has validation error
    const fieldHasError = (field: JC_FieldModel): boolean => {
        if (!submitClicked || !field.validate) return false;
        const errorMessage = field.validate(field.value);
        return !JC_Utils.stringNullOrEmpty(errorMessage);
    };

    // Create tile structure from sections and fields
    const getTileStructure = () => {
        const tiles: Array<{
            id: string;
            label: string;
            isHeading: boolean;
            field?: JC_FieldModel;
        }> = [];

        model.sections.forEach((section, sectionIndex) => {
            // Add section heading
            tiles.push({
                id: `section-${sectionIndex}`,
                label: section.Heading,
                isHeading: true
            });

            // Add fields for this section
            section.Fields.forEach(field => {
                tiles.push({
                    id: field.inputId,
                    label: field.label || field.inputId,
                    isHeading: false,
                    field: field
                });
            });
        });

        return tiles;
    };

    const tileStructure = getTileStructure();

    // Handle tile selection
    const handleTileClick = (tileId: string) => {
        const tile = tileStructure.find(t => t.id === tileId);
        if (tile && !tile.isHeading) {
            setSelectedFieldId(tileId);
        }
    };

    // Get selected tile
    const selectedTile = tileStructure.find(t => t.id === selectedFieldId);
    const selectedField = selectedTile?.field;

    // Handle field value changes
    const handleFieldChange = (newValue: string) => {
        if (selectedField && selectedField.onChange) {
            selectedField.onChange(newValue);
        }
    };

    // Handle form submission
    const handleSubmit = () => {
        if (!submitClicked) {
            setSubmitClicked(true);
        }

        // Check if all fields are valid
        const allFields = getAllFields();
        const allFieldsValid = allFields.every(field =>
            field.validate == null || JC_Utils.stringNullOrEmpty(field.validate(field.value))
        );

        if (allFieldsValid && model.onSubmit) {
            model.onSubmit();
            setSubmitClicked(false);
        }
    };

    return (
        <div className={styles.mainContainer}>
            {/* Header */}
            <div className={styles.header}>
                <h2 className={styles.headerLabel}>{model.headerLabel}</h2>
            </div>

            {/* Content Area */}
            <div className={styles.contentArea}>
                {/* Left Pane - Field Tiles */}
                <div className={styles.leftPane}>
                    <div className={styles.leftPaneHeader}>
                        {model.leftPaneHeader}
                    </div>
                    <div className={styles.tileContainer}>
                        {tileStructure.map((tile) => (
                            <div
                                key={tile.id}
                                className={`${styles.tile} ${
                                    tile.isHeading
                                        ? styles.headingTile
                                        : `${styles.fieldTile} ${selectedFieldId === tile.id ? styles.selectedTile : ''} ${
                                            tile.field && fieldHasError(tile.field) ? styles.errorTile : ''
                                        }`
                                }`}
                                onClick={() => handleTileClick(tile.id)}
                            >
                                {tile.isHeading ? (
                                    tile.label
                                ) : (
                                    <>
                                        <div className={styles.fieldLabel}>{tile.label}</div>
                                        <div className={styles.fieldValue}>
                                            {tile.field?.type === FieldTypeEnum.Dropdown && tile.field?.options && tile.field?.value
                                                ? tile.field.options.find(option => option.OptionId === tile.field?.value)?.Label || tile.field.value
                                                : tile.field?.value || "-"}
                                        </div>
                                    </>
                                )}
                            </div>
                        ))}
                    </div>
                </div>

                {/* Right Pane - Field Editor */}
                <div className={styles.rightPane}>
                    <div className={styles.rightPaneHeader}>
                        {selectedField ? selectedField.label || selectedField.inputId : "-"}
                    </div>
                    {selectedField ? (
                        <div className={styles.fieldEditor}>
                            {selectedField.type === FieldTypeEnum.Dropdown && selectedField.options ? (
                                <div className={styles.optionsList}>
                                    {selectedField.options.map((option) => (
                                        <div
                                            key={option.OptionId}
                                            className={`${styles.optionTile} ${
                                                selectedField.value === option.OptionId
                                                    ? styles.selectedOption
                                                    : ''
                                            }`}
                                            onClick={() => handleFieldChange(option.OptionId)}
                                        >
                                            {option.Label}
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <JC_Field
                                    inputId={selectedField.inputId}
                                    type={selectedField.type}
                                    label=""
                                    value={selectedField.value || ""}
                                    placeholder={selectedField.placeholder}
                                    readOnly={selectedField.readOnly}
                                    onChange={handleFieldChange}
                                    onBlur={selectedField.onBlur}
                                    onEnter={selectedField.onEnter}
                                    onEscape={selectedField.onEscape}
                                    validate={selectedField.validate}
                                    required={selectedField.required}
                                />
                            )}
                        </div>
                    ) : (
                        <div className={styles.emptyState}>
                            Select a field to edit
                        </div>
                    )}
                </div>
            </div>

            {/* Footer */}
            {(model.showSaveButton !== false || model.additionalFooterButtons) && (
                <div className={styles.footer}>
                    {/* Left Container - Additional Footer Buttons */}
                    <div className={styles.leftFooterContainer}>
                        {model.additionalFooterButtons && model.additionalFooterButtons.map((button, index) => (
                            <JC_Button
                                key={index}
                                text={button.text}
                                onClick={button.onClick}
                                isLoading={button.isLoading || false}
                            />
                        ))}
                    </div>

                    {/* Right Container - Error Message and Submit Button */}
                    <div className={styles.rightFooterContainer}>
                        {/* Error Message */}
                        {submitClicked && !JC_Utils.stringNullOrEmpty(getFirstValidationError()) && (
                            <div className={styles.errorMessage}>
                                {getFirstValidationError()}
                            </div>
                        )}

                        {/* Submit Button */}
                        {model.showSaveButton !== false && (
                            <JC_Button
                                text={model.submitButtonText || "Save"}
                                onClick={handleSubmit}
                                isLoading={model.isLoading || false}
                            />
                        )}
                    </div>
                </div>
            )}
        </div>
    );
}
